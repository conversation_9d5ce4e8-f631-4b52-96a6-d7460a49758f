<?php

class TestimonialsController extends AppController {

  var $name = 'Testimonials';
  var $components = array('Section', 'Navigation');

  public $criticalCss = 'testimonials';

  function getRecentTestimonials($limit = 10) {
    return $this->Testimonial->find('all', array(
      'limit' => $limit,
      'order' => array('date DESC'),
      'fields' => array('title', 'content', 'date'),
      'recursive' => -1
    ));
  }

  function index() {

    $this->paginate['Testimonial'] = array(
      'limit' => 10,
      'recursive' => 0,
      'order' => 'Testimonial.id DESC',
      'contain' => array('Image'),
    );

    $showSidebar = true;

    if (!empty($this->params['section'])) {

      $sectionModel = $this->Section->sectionSettings[$this->params['section']]['model'];

      $sectionForeignKey = Inflector::underscore($sectionModel).'_id';

      $joinTable = $this->{$this->modelClass}->hasAndBelongsToMany[$sectionModel]['joinTable'];

      $with = Inflector::classify($joinTable);

      $this->Testimonial->bindModel(array(
        'hasOne' => array(
          $with
        ),
      ), false);

      $this->paginate['Testimonial']['contain'][] = $with;

      $this->paginate['Testimonial']['order'] = $with.'.order';

      $this->paginate['Testimonial']['conditions'] = array(
        $with.'.'.$sectionForeignKey => $this->sectionId
      );

    } else {

      $this->__setNavigation();

      $SectionController = null;
      $SectionModel = null;
      $sectionData = null;
      $sectionSlug = null;
      $sectionSlugParam = null;

      $this->set(compact('sectionController', 'SectionModel', 'sectionData', 'sectionSlug','sectionSlugParam'));
    }

    $testimonials = $this->paginate('Testimonial');

    $this->_canonicalUrlForPaginated();

    if (empty($this->viewVars['breadcrumbs'])) {
      $breadcrumbs = array(array(
        'text' => "Testimonials",
        'url'  => $this->here
      ));
    }

    $heroBannerImage = RESOURCES_HOSTNAME . "/img/site/placeholders/spotlights.jpg";

    $sectionHeader = "Our customers say...";

    $hideTheImage = true;

    $this->set(compact('testimonials', 'breadcrumbs', 'heroBannerImage', 'sectionHeader', 'hideTheImage'));

    $this->_setMeta('Testimonials');

  }

  function view() {

    $showSidebar = true;

    $singularVar = Inflector::variable(Inflector::singularize($this->name));

    if (!$slug = $this->params[$singularVar.'_slug']) {
      $this->redirect(array('action' => 'index', 'destination_slug' => $this->params['destination_slug']));
    }

    if ($this->params['section']=='testimonials') {
      $this->__setNavigation();

      $showSidebar = false;
      $SectionModel = null;
      $sectionData = null;
      $sectionSlug= null;
      $sectionSlugParam = null;

      $this->set(compact('SectionModel', 'sectionData', 'sectionSlug','sectionSlugParam'));

    }

    $$singularVar = $this->{$this->modelNames[0]}->getBySlug($slug);

    if (empty($$singularVar)) {
      $this->cakeError('error404');
    }

    $this->_canonicalUrl(array(
      'testimonial_slug' => $slug,
      'section'          => 'destinations'
    ));

    if (empty($this->viewVars['breadcrumbs'])) {
      $breadcrumbs = array(
        array(
          'text' => "Testimonials",
          'url'  => Router::url(array(
            'controller' => 'testimonials',
            'action'     => 'index'
          ))
        ),
        array(
          'text' => ${$singularVar}['Testimonial']['title'],
          'url'  => $this->here
        )
      );
    }

    $heroBannerImage = RESOURCES_HOSTNAME . "/img/site/placeholders/spotlights.jpg";

    $sectionHeader = "Our customers say...";

    $hideTheImage = true;

    $this->set(compact($singularVar, 'showSidebar', 'breadcrumbs', 'heroBannerImage', 'sectionHeader', 'hideTheImage'));

    // Set noindex for all individual testimonial pages
    $this->noIndex = true;

    $this->_setMeta(${$singularVar}[$this->modelNames[0]]);

  }

  function __setNavigation() {

    $PageModel = ClassRegistry::init('Page');
    $pageData = $PageModel->findBySlug('testimonials');
    $navigation = $PageModel->getNavigation($pageData['Page']['id']);
    $noNav = false;
    if (count($navigation)>1) {
      $noNav = true;
      foreach ($navigation as $item) {
        if (isset($item['selected'])) {
          $navigation = array($item);
          break;
        }
      }
    }

    if ($noNav) {
      $navigation = array();
    }

    $this->set(compact('navigation'));
  }

}

?>
