<?php

// Timber::$cache = true;

Timber::$dirname = [
    'views',
    'views/templates',
    'views/components/common',
];

require_once('lumberjack/bootstrap.php');

/**
 * Enqueue custom CSS and JavaScript
 */
function bonvoyage_enqueue_assets() {
    $theme_directory = get_template_directory_uri();

    // Enqueue CSS
    wp_enqueue_style('bonvoyage-custom', $theme_directory . '/assets/css/custom.css', array(), '1.0.0');
    wp_enqueue_style('bonvoyage-search-toggle', $theme_directory . '/assets/css/search-toggle-fix.css', array(), filemtime(get_template_directory() . '/assets/css/search-toggle-fix.css'));

    // Enqueue JavaScript - load in header with priority 1 to run before other scripts
    wp_enqueue_script('bonvoyage-search-toggle', $theme_directory . '/assets/js/search-toggle.js', array(), filemtime(get_template_directory() . '/assets/js/search-toggle.js'), false);
}
add_action('wp_enqueue_scripts', 'bonvoyage_enqueue_assets', 1);

/**
 * Add inline script to the head to ensure our fix runs before any other scripts
 */
function bonvoyage_add_inline_script() {
    ?>
    <script>
    // Immediate execution to fix search toggle before any other scripts run
    (function() {
        // Set a flag to indicate this script has run
        window.searchToggleFixedInline = true;

        // Only run if our main script hasn't run yet
        if (!window.searchToggleFixed) {
            console.log('[Search Toggle] Inline script initializing');

            // Function to fix the search toggle
            function fixSearchToggle() {
                // Find the search toggle button
                const searchToggle = document.querySelector('.search-toggle');

                if (searchToggle) {
                    console.log('[Search Toggle] Inline: Found search toggle button');

                    // Remove data-toggle attribute to prevent the main site's JS from handling it
                    if (searchToggle.hasAttribute('data-toggle')) {
                        console.log('[Search Toggle] Inline: Removing data-toggle attribute');
                        searchToggle.removeAttribute('data-toggle');
                    }

                    // Mark this button as fixed
                    searchToggle.setAttribute('data-search-toggle-inline-fixed', 'true');

                    // Add our click event listener - only for mobile
                    searchToggle.addEventListener('click', function(e) {
                        if (window.innerWidth < 768) {
                            e.preventDefault();
                            e.stopPropagation();

                            // Toggle active class on button
                            this.classList.toggle('active');

                            // Toggle active class on search element
                            const searchElement = document.getElementById('search');
                            if (searchElement) {
                                searchElement.classList.toggle('active');

                                // Log the current state for debugging
                                const isActive = searchElement.classList.contains('active');
                                console.log('[Search Toggle] Inline: Toggled search form visibility. Active:', isActive);

                                // Force display style if needed
                                if (isActive) {
                                    console.log('[Search Toggle] Inline: Forcing display style to block');
                                    searchElement.style.display = 'block';
                                } else {
                                    searchElement.style.display = '';
                                }
                            } else {
                                console.error('[Search Toggle] Inline: Search element not found');
                                // Try to find by class as a fallback
                                const searchByClass = document.querySelector('.primary-search');
                                if (searchByClass) {
                                    console.log('[Search Toggle] Inline: Found search form by class');
                                    searchByClass.classList.toggle('active');
                                    if (searchByClass.classList.contains('active')) {
                                        searchByClass.style.display = 'block';
                                    }
                                }
                            }
                        } else {
                            console.log('[Search Toggle] Inline: Ignoring click on desktop - search form is always visible');
                        }
                    });
                } else {
                    // Button not found yet, try again later
                    console.log('[Search Toggle] Inline: Button not found yet, will try again on DOMContentLoaded');
                }
            }

            // Try to run immediately
            fixSearchToggle();

            // Also run on DOMContentLoaded as a fallback
            document.addEventListener('DOMContentLoaded', function() {
                // Only run if not already fixed by our main script
                if (!window.searchToggleFixed) {
                    console.log('[Search Toggle] Inline: Running on DOMContentLoaded');
                    fixSearchToggle();
                }
            });
        }
    })();
    </script>
    <?php
}
// Use priority 0 to ensure this runs as early as possible
add_action('wp_head', 'bonvoyage_add_inline_script', 0);

/**
 * Conditionally adds a custom <meta name="robots"> tag to the HTML head.
 *
 * For specific blog archive/category/author URLs, sets "noindex, follow".
 * For all other URLs, sets "noai, noimageai, SPC".
 *
 * Only runs when WordPress core/plugins aren't already handling robots meta tags.
 */
function bonvoyage_add_custom_robots_meta() {
    // Skip if WordPress core wp_robots function is handling robots meta tags
    if ( function_exists( 'wp_robots' ) && has_action( 'wp_head', 'wp_robots' ) ) {
        return;
    }

    // Skip if blog_public is set to 0 (WordPress will handle noindex)
    if ( get_option( 'blog_public' ) == '0' ) {
        return;
    }

    // Skip if Yoast SEO is active and handling robots
    if ( defined( 'WPSEO_VERSION' ) ) {
        return;
    }

    // Get the current request URI
    $request_uri = $_SERVER['REQUEST_URI'];

    // Build robots meta tag content as array
    $robots_content = [];

    // Define URL patterns that should be noindexed
    // Using an array of regex patterns for clarity and easy expansion
    $noindex_patterns = [
        // All Blog Category Pages - E.g: https://www.bon-voyage.co.uk/blog/category/america/
        '#^/blog/category/#',

        // All Blog Pagination Pages - E.g: https://www.bon-voyage.co.uk/blog/page/2/
        // Matches /blog/page/ followed by one or more digits, with an optional trailing slash
        '#^/blog/page/\d+/?#',

        // All Date Archive Pages - E.g: https://www.bon-voyage.co.uk/blog/2013/10/
        // Matches /blog/YYYY/, /blog/YYYY/MM/, or /blog/YYYY/MM/DD/
        '#^/blog/\d{4}/(\d{2}/)?(\d{2}/)?#',

        // All Author Pages - E.g: https://www.bon-voyage.co.uk/blog/author/karenniven/
        // Matches /blog/author/ and /blog/author/anything/
        '#^/blog/author/#',
    ];

    // Check if the current request URI matches any of the noindex patterns
    $match_found = false;
    foreach ( $noindex_patterns as $pattern ) {
        if ( preg_match( $pattern, $request_uri ) ) {
            $match_found = true;
            break; // Stop checking once a match is found
        }
    }

    // Set robots content based on pattern matching
    if ( $match_found ) {
        $robots_content = ['noindex', 'follow'];
    } else {
        $robots_content = ['noai', 'noimageai', 'SPC'];
    }

    // Output single robots meta tag
    echo '<meta name="robots" content="' . esc_attr( implode( ', ', $robots_content ) ) . '">' . "\n";
}
add_action( 'wp_head', 'bonvoyage_add_custom_robots_meta', 1 ); // Priority 1 ensures it runs very early
