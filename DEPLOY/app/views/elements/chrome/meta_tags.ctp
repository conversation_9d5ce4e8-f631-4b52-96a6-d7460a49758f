<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<title><?php echo $title; ?></title>

<?php
$requestRoute = Router::requestRoute();

// Build robots meta tag content
$robotsContent = [];

if (strpos($_SERVER['HTTP_HOST'], 'www.bon-voyage.co.uk') === false) {
    // Non-production environment
    $robotsContent = ['noindex', 'nofollow'];
} elseif (isset($noIndex) && $noIndex) {
    // Specific pages marked for noindex
    $robotsContent = ['noindex', 'follow'];
} else {
    // Default production behavior
    $robotsContent = ['noai', 'noimageai'];
}

// Output single robots meta tag
echo '<meta name="robots" content="' . implode(', ', $robotsContent) . '">';

if ($requestRoute[0] == '/') {
    echo $html->meta('keywords', $keywords);
}

echo $html->meta('description', $description);
echo $html->meta('icon');

echo $this->element('canonical');
?>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="google-site-verification" content="8Qky044688YQ4V7s1Fuw2Jy3pBxLRoIQPv-0FJTEeXs" />
<script type='application/ld+json'>
{
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "@id": "https://www.bon-voyage.co.uk/#business",
  "name": "Bon Voyage Travel & Tours Ltd",
  "url": "https://www.bon-voyage.co.uk/",
  "telephone": "+44 (0) 2380 248248",
  "faxNumber": "(023) 80 248249",
  "logo": "https://www.bon-voyage.co.uk/img/uploads/12810_fit588x588.jpg",
  "image": "https://lh3.googleusercontent.com/Mxw_em6V38diV85ZVnzoKfD19qDX-1IXbtz0sKv3eut8A6WE8cXJwxr3eDv8ZFP5-kPLfrHhTMF0=w1920-h1080-rw-no",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "16-18 Bellevue Road",
    "addressLocality": "Southampton",
    "addressRegion": "Hampshire",
    "postalCode": "SO15 2AY",
    "addressCountry": "GB"
  },
  "hasMap": "https://www.google.co.uk/maps/place/Bon+Voyage+Travel/@50.9124277,-1.4019383,15z/data=!4m5!3m4!1s0x0:0x6d1a3499fa1c9b4a!8m2!3d50.9124277!4d-1.4019383",
  "openingHours": "Mo, Tu, We, Th 09:00-18:00 Fr 09:00-17:30 Sa 09:00-15:00",
  "contactPoint": {
    "@type": "ContactPoint",
    "contactType": "reservations",
    "telephone": "+44 (0) 2380 248248"
  }<?php if (isset($feefoReviews['summary']) && $feefoReviews['summary']['count'] > 0): ?>,
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": <?php echo json_encode((float)$feefoReviews['summary']['average']); ?>,
    "bestRating": <?php echo json_encode((int)($feefoReviews['summary']['best'] ?? 5)); ?>,
    "worstRating": <?php echo json_encode((int)($feefoReviews['summary']['worst'] ?? 1)); ?>,
    "ratingCount": <?php echo json_encode((int)$feefoReviews['summary']['count']); ?>,
    "provider": {
      "@type": "Organization",
      "name": "Feefo",
      "url": "https://www.feefo.com/"
    }
  }<?php endif; ?>
}
</script>
