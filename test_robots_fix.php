<?php
/**
 * Test script to verify robots meta tag fix
 * This simulates the logic from meta_tags.ctp to ensure single robots tag output
 */

echo "=== TESTING ROBOTS META TAG FIX ===\n\n";

// Test scenarios
$test_scenarios = [
    [
        'description' => 'Production site with noIndex=false',
        'host' => 'www.bon-voyage.co.uk',
        'noIndex' => false,
        'expected' => 'noai, noimageai'
    ],
    [
        'description' => 'Production site with noIndex=true (subscriptions page)',
        'host' => 'www.bon-voyage.co.uk', 
        'noIndex' => true,
        'expected' => 'noindex, follow'
    ],
    [
        'description' => 'Development site (any page)',
        'host' => 'bon-voyage.ddev.site',
        'noIndex' => false,
        'expected' => 'noindex, nofollow'
    ],
    [
        'description' => 'Development site with noIndex=true',
        'host' => 'bon-voyage.ddev.site',
        'noIndex' => true,
        'expected' => 'noindex, nofollow'
    ]
];

function generateRobotsContent($host, $noIndex) {
    // Build robots meta tag content (same logic as meta_tags.ctp)
    $robotsContent = [];

    if (strpos($host, 'www.bon-voyage.co.uk') === false) {
        // Non-production environment
        $robotsContent = ['noindex', 'nofollow'];
    } elseif (isset($noIndex) && $noIndex) {
        // Specific pages marked for noindex
        $robotsContent = ['noindex', 'follow'];
    } else {
        // Default production behavior
        $robotsContent = ['noai', 'noimageai'];
    }

    return implode(', ', $robotsContent);
}

$allPassed = true;

foreach ($test_scenarios as $i => $scenario) {
    $result = generateRobotsContent($scenario['host'], $scenario['noIndex']);
    $passed = $result === $scenario['expected'];
    
    if (!$passed) {
        $allPassed = false;
    }
    
    echo "Test " . ($i + 1) . ": " . $scenario['description'] . "\n";
    echo "  Expected: " . $scenario['expected'] . "\n";
    echo "  Got:      " . $result . "\n";
    echo "  Status:   " . ($passed ? "✅ PASS" : "❌ FAIL") . "\n\n";
}

echo "=== SUMMARY ===\n";
echo "Overall result: " . ($allPassed ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED") . "\n\n";

echo "=== BEFORE vs AFTER ===\n";
echo "BEFORE (duplicate tags):\n";
echo "  <meta name=\"robots\" content=\"noai, noimageai\">\n";
echo "  <meta name=\"robots\" content=\"noindex, follow\">\n\n";

echo "AFTER (single merged tag):\n";
echo "  <meta name=\"robots\" content=\"noindex, follow\">\n\n";

echo "=== KEY BENEFITS ===\n";
echo "✅ No more duplicate robots meta tags\n";
echo "✅ Single source of truth for robots directives\n";
echo "✅ Most restrictive directive (noindex) properly applied\n";
echo "✅ Cleaner HTML output\n";
echo "✅ Better SEO compliance\n";

?>
